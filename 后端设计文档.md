# 积分管理系统后端设计建议

## 目录
- [技术栈选择](#技术栈选择)
- [系统架构](#系统架构)
- [数据库设计](#数据库设计)
- [API接口设计](#api接口设计)
- [权限管理](#权限管理)
- [业务逻辑设计](#业务逻辑设计)
- [部署建议](#部署建议)
- [安全考虑](#安全考虑)
- [性能优化](#性能优化)

## 技术栈选择

### 推荐技术栈
```
后端框架：Node.js + Express.js / Koa.js 或 Python + FastAPI / Django
数据库：MySQL 8.0+ 或 PostgreSQL 14+
缓存：Redis 6.0+
消息队列：Redis + Bull Queue 或 RabbitMQ
API文档：Swagger/OpenAPI 3.0
监控：Grafana + Prometheus
日志：Winston + ELK Stack
部署：Docker + Kubernetes 或 PM2
```

### 技术选型理由
- **Node.js + Express**: 与前端技术栈统一，开发效率高，生态丰富
- **MySQL**: 企业级稳定性，支持事务，适合积分系统的强一致性需求
- **Redis**: 高性能缓存，适合积分排名、频繁查询数据
- **Docker**: 容器化部署，环境一致性好

## 系统架构

### 整体架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端应用       │    │   管理后台       │    │   移动端应用     │
│   (Nuxt.js)     │    │   (Admin Panel)  │    │   (H5/小程序)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                        │                        │
         └────────────────────────┼────────────────────────┘
                                  │
                     ┌─────────────────┐
                     │   API Gateway    │
                     │   (Nginx/Kong)   │
                     └─────────────────┘
                                  │
         ┌────────────────────────┼────────────────────────┐
         │                        │                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户服务       │    │   积分服务       │    │   商品服务       │
│  (User Service)  │    │ (Points Service) │    │ (Product Service)│
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                        │                        │
         └────────────────────────┼────────────────────────┘
                                  │
                     ┌─────────────────┐
                     │   数据访问层     │
                     │   (Data Layer)   │
                     └─────────────────┘
                                  │
         ┌────────────────────────┼────────────────────────┐
         │                        │                        │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MySQL数据库    │    │   Redis缓存      │    │   文件存储       │
│   (主数据库)     │    │   (缓存/排名)    │    │   (OSS/MinIO)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 微服务划分
1. **用户服务 (User Service)**
   - 用户注册、登录、认证
   - 用户信息管理
   - 权限管理

2. **积分服务 (Points Service)**
   - 积分规则管理
   - 积分计算和发放
   - 积分记录和查询
   - 积分排名

3. **商品服务 (Product Service)**
   - 商品管理
   - 兑换管理
   - 库存管理

4. **通知服务 (Notification Service)**
   - 消息推送
   - 邮件通知
   - 站内信

## 数据库设计

### 核心表结构

#### 1. 用户表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    employee_id VARCHAR(20) UNIQUE NOT NULL COMMENT '员工工号',
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    department VARCHAR(100) COMMENT '部门',
    position VARCHAR(100) COMMENT '职位',
    avatar_url VARCHAR(255) COMMENT '头像URL',
    role ENUM('employee', 'manager', 'admin') DEFAULT 'employee' COMMENT '角色',
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '状态',
    hire_date DATE COMMENT '入职日期',
    current_points INT DEFAULT 0 COMMENT '当前积分',
    total_earned_points INT DEFAULT 0 COMMENT '累计获得积分',
    total_spent_points INT DEFAULT 0 COMMENT '累计消费积分',
    points_level ENUM('bronze', 'silver', 'gold', 'platinum') DEFAULT 'bronze' COMMENT '积分等级',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_employee_id (employee_id),
    INDEX idx_department (department),
    INDEX idx_current_points (current_points),
    INDEX idx_status (status)
) COMMENT='用户表';
```

#### 2. 积分规则表 (points_rules)
```sql
CREATE TABLE points_rules (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    rule_code VARCHAR(50) UNIQUE NOT NULL COMMENT '规则代码',
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    category ENUM('customer_happiness', 'colleague_happiness', 'leader_happiness', 'deduction') NOT NULL COMMENT '规则分类',
    points_value INT NOT NULL COMMENT '积分值(正数为加分，负数为扣分)',
    description TEXT COMMENT '规则描述',
    conditions JSON COMMENT '触发条件',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    requires_approval BOOLEAN DEFAULT FALSE COMMENT '是否需要审批',
    max_times_per_day INT COMMENT '每日最大次数',
    max_times_per_month INT COMMENT '每月最大次数',
    valid_from DATE COMMENT '生效开始日期',
    valid_to DATE COMMENT '生效结束日期',
    created_by BIGINT NOT NULL COMMENT '创建人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_category (category),
    INDEX idx_is_active (is_active),
    INDEX idx_rule_code (rule_code),
    FOREIGN KEY (created_by) REFERENCES users(id)
) COMMENT='积分规则表';
```

#### 3. 积分记录表 (points_records)
```sql
CREATE TABLE points_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    rule_id BIGINT COMMENT '规则ID',
    type ENUM('earn', 'spend', 'adjustment', 'expire') NOT NULL COMMENT '记录类型',
    points_change INT NOT NULL COMMENT '积分变更(正数为增加，负数为减少)',
    points_before INT NOT NULL COMMENT '变更前积分',
    points_after INT NOT NULL COMMENT '变更后积分',
    reason VARCHAR(255) NOT NULL COMMENT '变更原因',
    description TEXT COMMENT '详细描述',
    reference_id BIGINT COMMENT '关联业务ID(如订单ID)',
    reference_type VARCHAR(50) COMMENT '关联业务类型',
    operator_id BIGINT COMMENT '操作人ID',
    status ENUM('pending', 'approved', 'rejected', 'completed') DEFAULT 'completed' COMMENT '状态',
    evidence_url VARCHAR(255) COMMENT '证明材料URL',
    reviewed_by BIGINT COMMENT '审核人ID',
    reviewed_at TIMESTAMP NULL COMMENT '审核时间',
    review_notes TEXT COMMENT '审核备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_rule_id (rule_id),
    INDEX idx_type (type),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_user_created (user_id, created_at),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (rule_id) REFERENCES points_rules(id),
    FOREIGN KEY (operator_id) REFERENCES users(id),
    FOREIGN KEY (reviewed_by) REFERENCES users(id)
) COMMENT='积分记录表';
```

#### 4. 商品表 (products)
```sql
CREATE TABLE products (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(200) NOT NULL COMMENT '商品名称',
    description TEXT COMMENT '商品描述',
    category_id BIGINT COMMENT '分类ID',
    points_price INT NOT NULL COMMENT '积分价格',
    cash_price DECIMAL(10,2) COMMENT '现金价格',
    stock_quantity INT DEFAULT 0 COMMENT '库存数量',
    is_virtual BOOLEAN DEFAULT FALSE COMMENT '是否虚拟商品',
    status ENUM('active', 'inactive', 'out_of_stock') DEFAULT 'active' COMMENT '状态',
    image_urls JSON COMMENT '商品图片URLs',
    sort_order INT DEFAULT 0 COMMENT '排序',
    is_featured BOOLEAN DEFAULT FALSE COMMENT '是否推荐',
    max_exchange_per_user INT COMMENT '每人最大兑换数量',
    valid_from TIMESTAMP COMMENT '有效期开始',
    valid_to TIMESTAMP COMMENT '有效期结束',
    created_by BIGINT NOT NULL COMMENT '创建人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_category_id (category_id),
    INDEX idx_status (status),
    INDEX idx_is_featured (is_featured),
    INDEX idx_points_price (points_price),
    FOREIGN KEY (created_by) REFERENCES users(id)
) COMMENT='商品表';
```

#### 5. 兑换记录表 (exchange_records)
```sql
CREATE TABLE exchange_records (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    product_id BIGINT NOT NULL COMMENT '商品ID',
    quantity INT NOT NULL DEFAULT 1 COMMENT '兑换数量',
    points_cost INT NOT NULL COMMENT '消耗积分',
    status ENUM('pending', 'processing', 'shipped', 'completed', 'cancelled') DEFAULT 'pending' COMMENT '状态',
    delivery_info JSON COMMENT '收货信息',
    tracking_number VARCHAR(100) COMMENT '快递单号',
    exchange_code VARCHAR(50) COMMENT '兑换码',
    notes TEXT COMMENT '备注',
    processed_by BIGINT COMMENT '处理人ID',
    processed_at TIMESTAMP NULL COMMENT '处理时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_product_id (product_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (product_id) REFERENCES products(id),
    FOREIGN KEY (processed_by) REFERENCES users(id)
) COMMENT='兑换记录表';
```

## API接口设计

### 接口规范

#### RESTful API设计原则
- 使用HTTP动词：GET（查询）、POST（创建）、PUT（更新）、DELETE（删除）
- 状态码规范：200成功、201创建、400参数错误、401未授权、403禁止、404不存在、500服务器错误
- 统一响应格式

#### 统一响应格式
```json
{
    "success": true,
    "code": 200,
    "message": "操作成功",
    "data": {},
    "timestamp": "2024-01-20T10:30:00Z",
    "requestId": "req_123456789"
}
```

### 核心API接口

#### 1. 用户认证模块

```javascript
// POST /api/auth/login - 用户登录
{
    "username": "zhangsan",
    "password": "password123"
}

// POST /api/auth/logout - 用户登出
// Headers: Authorization: Bearer <token>

// GET /api/auth/profile - 获取当前用户信息
// Headers: Authorization: Bearer <token>

// PUT /api/auth/profile - 更新用户信息
{
    "real_name": "张三",
    "email": "<EMAIL>",
    "phone": "13800138000"
}
```

#### 2. 积分管理模块

```javascript
// GET /api/points/balance - 获取用户积分余额
// Response:
{
    "current_points": 1250,
    "total_earned": 2500,
    "total_spent": 1250,
    "points_level": "silver",
    "level_progress": {
        "current_level": "silver",
        "next_level": "gold",
        "points_needed": 750
    }
}

// GET /api/points/records - 获取积分记录
// Query params: page, limit, type, start_date, end_date
{
    "records": [
        {
            "id": 1,
            "type": "earn",
            "points_change": 50,
            "reason": "顾客转介绍新客户成交",
            "created_at": "2024-01-20T10:30:00Z"
        }
    ],
    "total": 100,
    "page": 1,
    "limit": 20
}

// POST /api/points/award - 奖励积分（管理员）
{
    "user_id": 123,
    "rule_code": "customer_referral",
    "points": 50,
    "reason": "推荐新客户成功",
    "evidence_url": "https://example.com/evidence.jpg"
}

// POST /api/points/deduct - 扣除积分（管理员）
{
    "user_id": 123,
    "rule_code": "late_penalty",
    "points": -30,
    "reason": "迟到扣分"
}

// GET /api/points/ranking - 积分排行榜
// Query params: page, limit, department
{
    "rankings": [
        {
            "rank": 1,
            "user_id": 123,
            "real_name": "张三",
            "department": "销售部",
            "current_points": 2500,
            "avatar_url": "https://example.com/avatar.jpg"
        }
    ]
}
```

#### 3. 积分规则管理

```javascript
// GET /api/points/rules - 获取积分规则列表
// Query params: category, is_active

// GET /api/points/rules/:id - 获取单个规则详情

// POST /api/points/rules - 创建积分规则（管理员）
{
    "rule_code": "customer_gift",
    "rule_name": "给顾客送礼物",
    "category": "customer_happiness",
    "points_value": 20,
    "description": "节日当天、生日、平常给顾客或顾客家人送礼物",
    "conditions": {
        "gift_value_ranges": [
            {"min": 0, "max": 100, "points": 5},
            {"min": 100, "max": 200, "points": 10},
            {"min": 200, "max": 500, "points": 20},
            {"min": 500, "max": null, "points": 30}
        ]
    },
    "max_times_per_month": 10
}

// PUT /api/points/rules/:id - 更新积分规则

// DELETE /api/points/rules/:id - 删除积分规则
```

#### 4. 商品管理模块

```javascript
// GET /api/products - 获取商品列表
// Query params: category_id, status, is_featured, page, limit

// GET /api/products/:id - 获取商品详情

// POST /api/products - 创建商品（管理员）
{
    "name": "iPhone 15",
    "description": "最新款iPhone",
    "category_id": 1,
    "points_price": 15000,
    "cash_price": 5999.00,
    "stock_quantity": 10,
    "image_urls": ["https://example.com/iphone.jpg"],
    "max_exchange_per_user": 1
}

// POST /api/products/:id/exchange - 兑换商品
{
    "quantity": 1,
    "delivery_info": {
        "name": "张三",
        "phone": "13800138000",
        "address": "北京市朝阳区xxx路xxx号"
    }
}

// GET /api/exchanges - 获取兑换记录
// Query params: status, page, limit

// PUT /api/exchanges/:id/status - 更新兑换状态（管理员）
{
    "status": "shipped",
    "tracking_number": "SF1234567890",
    "notes": "已发货"
}
```

#### 5. 管理后台API

```javascript
// GET /api/admin/dashboard - 管理台统计数据
{
    "overview": {
        "total_users": 500,
        "active_users": 450,
        "total_points_issued": 125000,
        "total_exchanges": 1200
    },
    "recent_activities": [...],
    "top_earners": [...],
    "popular_products": [...]
}

// GET /api/admin/users - 用户管理列表
// Query params: department, status, role, search, page, limit

// PUT /api/admin/users/:id - 更新用户信息
{
    "real_name": "张三",
    "department": "销售部",
    "position": "销售经理",
    "role": "manager",
    "status": "active"
}

// POST /api/admin/users/:id/points/adjust - 手动调整积分
{
    "points_change": 100,
    "reason": "年终奖励",
    "operator_notes": "年度优秀员工奖励"
}

// GET /api/admin/reports/points - 积分报表
// Query params: start_date, end_date, department, export_format

// GET /api/admin/audit-logs - 操作日志
// Query params: operator_id, action, start_date, end_date, page, limit
```

## 权限管理

### 角色权限设计

#### 角色定义
```javascript
const ROLES = {
    EMPLOYEE: 'employee',    // 普通员工
    MANAGER: 'manager',      // 部门经理
    ADMIN: 'admin'          // 系统管理员
};

const PERMISSIONS = {
    // 积分相关
    'points:view_own': '查看自己积分',
    'points:view_all': '查看所有人积分',
    'points:award': '奖励积分',
    'points:deduct': '扣除积分',
    'points:adjust': '调整积分',
    
    // 用户管理
    'users:view': '查看用户列表',
    'users:create': '创建用户',
    'users:update': '更新用户',
    'users:delete': '删除用户',
    
    // 商品管理
    'products:view': '查看商品',
    'products:create': '创建商品',
    'products:update': '更新商品',
    'products:delete': '删除商品',
    'products:exchange': '兑换商品',
    
    // 规则管理
    'rules:view': '查看规则',
    'rules:create': '创建规则',
    'rules:update': '更新规则',
    'rules:delete': '删除规则',
    
    // 报表
    'reports:view': '查看报表',
    'reports:export': '导出报表'
};

// 角色权限映射
const ROLE_PERMISSIONS = {
    [ROLES.EMPLOYEE]: [
        'points:view_own',
        'products:view',
        'products:exchange'
    ],
    [ROLES.MANAGER]: [
        ...ROLE_PERMISSIONS[ROLES.EMPLOYEE],
        'points:view_all',
        'points:award',
        'points:deduct',
        'users:view',
        'reports:view'
    ],
    [ROLES.ADMIN]: [
        ...ROLE_PERMISSIONS[ROLES.MANAGER],
        'points:adjust',
        'users:create',
        'users:update',
        'users:delete',
        'products:create',
        'products:update',
        'products:delete',
        'rules:view',
        'rules:create',
        'rules:update',
        'rules:delete',
        'reports:export'
    ]
};
```

### JWT Token设计
```javascript
// JWT Payload
{
    "user_id": 123,
    "username": "zhangsan",
    "role": "employee",
    "department": "销售部",
    "permissions": ["points:view_own", "products:view"],
    "iat": 1640995200,
    "exp": 1641081600
}
```

## 业务逻辑设计

### 积分计算引擎

```javascript
class PointsEngine {
    // 计算积分
    async calculatePoints(ruleCode, context) {
        const rule = await this.getRuleByCode(ruleCode);
        
        if (!rule || !rule.is_active) {
            throw new Error('规则不存在或已禁用');
        }
        
        // 检查触发条件
        if (!this.checkConditions(rule.conditions, context)) {
            throw new Error('不满足触发条件');
        }
        
        // 检查频次限制
        await this.checkFrequencyLimits(rule, context.user_id);
        
        // 计算积分值
        let points = rule.points_value;
        
        // 特殊规则处理（如送礼物按金额分档）
        if (rule.rule_code === 'customer_gift' && context.gift_value) {
            points = this.calculateGiftPoints(context.gift_value, rule.conditions);
        }
        
        return {
            points,
            rule_id: rule.id,
            requires_approval: rule.requires_approval
        };
    }
    
    // 发放积分
    async awardPoints(userId, ruleCode, context, operatorId) {
        const calculation = await this.calculatePoints(ruleCode, {
            ...context,
            user_id: userId
        });
        
        if (calculation.requires_approval) {
            // 创建待审批记录
            return await this.createPendingRecord(userId, calculation, context, operatorId);
        } else {
            // 直接发放
            return await this.grantPoints(userId, calculation, context, operatorId);
        }
    }
    
    // 直接发放积分
    async grantPoints(userId, calculation, context, operatorId) {
        const user = await this.getUserById(userId);
        
        const record = await this.db.transaction(async (trx) => {
            // 创建积分记录
            const record = await trx('points_records').insert({
                user_id: userId,
                rule_id: calculation.rule_id,
                type: 'earn',
                points_change: calculation.points,
                points_before: user.current_points,
                points_after: user.current_points + calculation.points,
                reason: context.reason,
                description: context.description,
                operator_id: operatorId,
                status: 'completed'
            });
            
            // 更新用户积分
            await trx('users').where('id', userId).update({
                current_points: user.current_points + calculation.points,
                total_earned_points: user.total_earned_points + calculation.points,
                updated_at: new Date()
            });
            
            return record;
        });
        
        // 检查并更新用户等级
        await this.updateUserLevel(userId);
        
        // 更新缓存排名
        await this.updateRanking(userId);
        
        // 发送通知
        await this.sendNotification(userId, 'points_earned', {
            points: calculation.points,
            reason: context.reason
        });
        
        return record;
    }
}
```

### 积分等级系统

```javascript
class PointsLevelService {
    // 等级配置
    LEVELS = [
        { name: 'bronze', min_points: 0, max_points: 999, benefits: ['基础权益'] },
        { name: 'silver', min_points: 1000, max_points: 2999, benefits: ['优先选择权'] },
        { name: 'gold', min_points: 3000, max_points: 5999, benefits: ['专属商品'] },
        { name: 'platinum', min_points: 6000, max_points: null, benefits: ['全部特权'] }
    ];
    
    // 根据积分计算等级
    calculateLevel(points) {
        for (const level of this.LEVELS) {
            if (points >= level.min_points && 
                (level.max_points === null || points <= level.max_points)) {
                return level;
            }
        }
        return this.LEVELS[0]; // 默认青铜
    }
    
    // 更新用户等级
    async updateUserLevel(userId) {
        const user = await this.getUserById(userId);
        const newLevel = this.calculateLevel(user.current_points);
        
        if (user.points_level !== newLevel.name) {
            await this.db('users').where('id', userId).update({
                points_level: newLevel.name
            });
            
            // 发送升级通知
            await this.sendLevelUpNotification(userId, newLevel);
        }
    }
}
```

### 兑换业务流程

```javascript
class ExchangeService {
    async exchangeProduct(userId, productId, quantity, deliveryInfo) {
        const user = await this.getUserById(userId);
        const product = await this.getProductById(productId);
        
        // 检查商品状态
        if (product.status !== 'active') {
            throw new Error('商品不可兑换');
        }
        
        // 检查库存
        if (product.stock_quantity < quantity) {
            throw new Error('库存不足');
        }
        
        // 检查积分余额
        const totalCost = product.points_price * quantity;
        if (user.current_points < totalCost) {
            throw new Error('积分不足');
        }
        
        // 检查兑换限制
        await this.checkExchangeLimit(userId, productId, quantity);
        
        // 执行兑换
        const exchangeRecord = await this.db.transaction(async (trx) => {
            // 创建兑换记录
            const record = await trx('exchange_records').insert({
                user_id: userId,
                product_id: productId,
                quantity: quantity,
                points_cost: totalCost,
                status: 'pending',
                delivery_info: JSON.stringify(deliveryInfo),
                exchange_code: this.generateExchangeCode()
            });
            
            // 扣除积分
            await trx('points_records').insert({
                user_id: userId,
                type: 'spend',
                points_change: -totalCost,
                points_before: user.current_points,
                points_after: user.current_points - totalCost,
                reason: `兑换商品：${product.name}`,
                reference_id: record.id,
                reference_type: 'exchange',
                status: 'completed'
            });
            
            // 更新用户积分
            await trx('users').where('id', userId).update({
                current_points: user.current_points - totalCost,
                total_spent_points: user.total_spent_points + totalCost
            });
            
            // 更新商品库存
            await trx('products').where('id', productId).decrement('stock_quantity', quantity);
            
            return record;
        });
        
        // 发送兑换成功通知
        await this.sendExchangeNotification(userId, exchangeRecord);
        
        return exchangeRecord;
    }
}
```

## 部署建议

### Docker化部署

#### Dockerfile
```dockerfile
# Node.js 后端 Dockerfile
FROM node:18-alpine

WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

EXPOSE 3000

CMD ["npm", "start"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  # 后端API服务
  api:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_NAME=points_system
      - DB_USER=points_user
      - DB_PASSWORD=secure_password
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - JWT_SECRET=your_jwt_secret
    depends_on:
      - mysql
      - redis
    restart: unless-stopped
    
  # MySQL数据库
  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=points_system
      - MYSQL_USER=points_user
      - MYSQL_PASSWORD=secure_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "3306:3306"
    restart: unless-stopped
    
  # Redis缓存
  redis:
    image: redis:6.2-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    
  # Nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
```

### 生产环境配置

#### 环境变量配置
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=points_system
DB_USER=points_user
DB_PASSWORD=secure_password
DB_POOL_MIN=5
DB_POOL_MAX=20

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=redis_password
REDIS_DB=0

# JWT配置
JWT_SECRET=your_super_secret_jwt_key
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# 文件上传配置
UPLOAD_MAX_SIZE=10485760  # 10MB
UPLOAD_ALLOWED_TYPES=jpg,jpeg,png,pdf,doc,docx

# 邮件配置
SMTP_HOST=smtp.company.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=smtp_password

# 短信配置
SMS_PROVIDER=aliyun
SMS_ACCESS_KEY=your_access_key
SMS_ACCESS_SECRET=your_access_secret

# 对象存储配置
OSS_ENDPOINT=https://oss-cn-beijing.aliyuncs.com
OSS_BUCKET=points-system-files
OSS_ACCESS_KEY=your_oss_key
OSS_ACCESS_SECRET=your_oss_secret

# 监控配置
LOG_LEVEL=info
METRICS_ENABLED=true
TRACE_ENABLED=true
```

## 安全考虑

### 1. 数据安全
- 密码使用bcrypt加密存储
- 敏感数据传输使用HTTPS
- 数据库连接使用SSL
- 定期备份数据库

### 2. 接口安全
- JWT Token认证
- API限流和防刷
- 输入参数验证和过滤
- SQL注入防护
- XSS攻击防护

### 3. 业务安全
- 积分操作日志记录
- 重要操作需要二次确认
- 积分异常监控和告警
- 定期安全审计

### 4. 权限控制
```javascript
// 权限中间件示例
const requirePermission = (permission) => {
    return async (req, res, next) => {
        const user = req.user;
        
        if (!user) {
            return res.status(401).json({
                success: false,
                message: '未登录'
            });
        }
        
        if (!user.permissions.includes(permission)) {
            return res.status(403).json({
                success: false,
                message: '权限不足'
            });
        }
        
        next();
    };
};

// 使用示例
app.post('/api/points/award', 
    requireAuth, 
    requirePermission('points:award'), 
    awardPointsController
);
```

## 性能优化

### 1. 数据库优化
- 合理使用索引
- 查询优化，避免N+1问题
- 数据库连接池
- 读写分离
- 分表分库策略

### 2. 缓存策略
```javascript
// Redis缓存策略
class CacheService {
    // 用户积分缓存
    async getUserPoints(userId) {
        const cacheKey = `user:points:${userId}`;
        let points = await redis.get(cacheKey);
        
        if (!points) {
            const user = await db('users').where('id', userId).first();
            points = user.current_points;
            await redis.setex(cacheKey, 300, points); // 5分钟过期
        }
        
        return parseInt(points);
    }
    
    // 积分排行榜缓存
    async getPointsRanking(limit = 100) {
        const cacheKey = 'ranking:points';
        let ranking = await redis.zrevrange(cacheKey, 0, limit - 1, 'WITHSCORES');
        
        if (ranking.length === 0) {
            // 从数据库重建排行榜
            const users = await db('users')
                .select('id', 'current_points')
                .where('status', 'active')
                .orderBy('current_points', 'desc')
                .limit(1000);
                
            const pipeline = redis.pipeline();
            users.forEach(user => {
                pipeline.zadd(cacheKey, user.current_points, user.id);
            });
            await pipeline.exec();
            await redis.expire(cacheKey, 3600); // 1小时过期
            
            ranking = await redis.zrevrange(cacheKey, 0, limit - 1, 'WITHSCORES');
        }
        
        return ranking;
    }
}
```

### 3. 接口优化
- 分页查询
- 字段筛选
- 数据压缩
- CDN加速
- 接口缓存

### 4. 监控告警
```javascript
// 性能监控中间件
const performanceMonitor = (req, res, next) => {
    const start = Date.now();
    
    res.on('finish', () => {
        const duration = Date.now() - start;
        
        // 记录慢查询
        if (duration > 1000) {
            logger.warn('Slow API detected', {
                method: req.method,
                url: req.url,
                duration,
                user_id: req.user?.id
            });
        }
        
        // 记录到监控系统
        metrics.histogram('api_duration', duration, {
            method: req.method,
            route: req.route?.path,
            status_code: res.statusCode
        });
    });
    
    next();
};
```

## 总结

这个后端设计建议涵盖了积分管理系统的核心功能，包括：

1. **完整的技术架构**：从技术栈选择到微服务划分
2. **详细的数据库设计**：核心表结构和关系设计
3. **RESTful API设计**：标准化的接口规范和示例
4. **业务逻辑实现**：积分计算引擎、等级系统、兑换流程
5. **权限管理系统**：角色权限设计和JWT认证
6. **部署和运维**：Docker化部署和生产环境配置
7. **安全和性能**：全面的安全考虑和性能优化策略

建议在实施时根据实际业务需求和团队技术栈进行适当调整，并采用敏捷开发方式逐步迭代完善。 